<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Helpers\FilterHelper;
use App\Enums\FilterFieldType;
use Illuminate\Http\Request;

class FilterHelperSimpleTest extends TestCase
{
    private $filterHelper;

    protected function setUp(): void
    {
        parent::setUp();
        $this->filterHelper = new FilterHelper();
    }

    public function test_it_requires_type_in_config()
    {
        $request = new Request(['keyword' => 'test']);
        
        // Type mặc định là text nếu không truyền
        $config = [
            ['field' => 'keyword'] // Không có type
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);
        
        // Vẫn hoạt động với type mặc định là text
        $this->assertStringContainsString('test', $result);
    }

    public function test_it_handles_text_type_without_max_length()
    {
        $request = new Request(['keyword' => 'this is a very long text that should not be truncated by default']);
        
        $config = [
            ['field' => 'keyword', 'type' => 'text'] // Không có max_length
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);
        
        // Text không bị cắt vì không có max_length mặc định
        $this->assertStringContainsString('this is a very long text that should not be truncated by default', $result);
    }

    public function test_it_handles_text_type_with_max_length_option()
    {
        $request = new Request(['keyword' => 'this is a very long text']);
        
        $config = [
            ['field' => 'keyword', 'type' => 'text', 'options' => ['max_length' => 10]]
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);
        
        // Text bị cắt khi có max_length trong options
        $this->assertStringContainsString('this is a ', $result);
        $this->assertStringContainsString('...', $result);
    }

    public function test_it_handles_ids_type()
    {
        $request = new Request(['user_ids' => '1,2,3']);
        
        $config = [
            ['field' => 'user_ids', 'type' => 'ids', 'options' => ['prefix' => '#']]
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);
        
        $this->assertStringContainsString('#1', $result);
        $this->assertStringContainsString('#2', $result);
        $this->assertStringContainsString('#3', $result);
    }

    public function test_it_handles_array_type()
    {
        $request = new Request(['tags' => ['vip', 'new']]);
        
        $config = [
            ['field' => 'tags', 'type' => 'array', 'options' => [
                'mapping' => ['vip' => 'VIP Customer', 'new' => 'New Customer']
            ]]
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);
        
        $this->assertStringContainsString('VIP Customer', $result);
        $this->assertStringContainsString('New Customer', $result);
    }

    public function test_it_handles_select_type()
    {
        $request = new Request(['status' => 'active']);
        
        $config = [
            ['field' => 'status', 'type' => 'select', 'options' => [
                'mapping' => ['active' => 'Đang hoạt động', 'inactive' => 'Không hoạt động']
            ]]
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);
        
        $this->assertStringContainsString('Đang hoạt động', $result);
    }

    public function test_it_handles_boolean_type()
    {
        $request = new Request(['is_verified' => true]);
        
        $config = [
            ['field' => 'is_verified', 'type' => 'boolean', 'options' => [
                'labels' => ['Chưa xác thực', 'Đã xác thực']
            ]]
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);
        
        $this->assertStringContainsString('Đã xác thực', $result);
    }

    public function test_it_handles_date_type()
    {
        $request = new Request(['created_date' => '2023-12-25']);
        
        $config = [
            ['field' => 'created_date', 'type' => 'date', 'options' => ['format' => 'd/m/Y']]
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);
        
        $this->assertStringContainsString('25/12/2023', $result);
    }

    public function test_it_handles_range_type()
    {
        $request = new Request(['price_range' => ['100', '500']]);
        
        $config = [
            ['field' => 'price_range', 'type' => 'range', 'options' => [
                'separator' => ' - ', 'suffix' => ' USD'
            ]]
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);
        
        $this->assertStringContainsString('100 - 500 USD', $result);
    }

    public function test_enum_helper_methods()
    {
        // Test getAllTypes
        $types = FilterFieldType::getAllTypes();
        $this->assertContains('text', $types);
        $this->assertContains('ids', $types);
        $this->assertContains('array', $types);
        
        // Test isValid
        $this->assertTrue(FilterFieldType::isValid('text'));
        $this->assertTrue(FilterFieldType::isValid('ids'));
        $this->assertFalse(FilterFieldType::isValid('invalid'));
        
        // Test createConfig
        $config = FilterFieldType::createConfig('test', FilterFieldType::TEXT, ['max_length' => 20]);
        $this->assertEquals('test', $config['field']);
        $this->assertEquals('text', $config['type']);
        $this->assertEquals(['max_length' => 20], $config['options']);
    }

    public function test_switch_case_can_be_extended()
    {
        // Test rằng switch case hoạt động với type không có trong enum
        $request = new Request(['custom_field' => 'test value']);
        
        $config = [
            ['field' => 'custom_field', 'type' => 'unknown_type'] // Type không có trong switch
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);
        
        // Sẽ fallback về default (text)
        $this->assertStringContainsString('test value', $result);
    }
}
