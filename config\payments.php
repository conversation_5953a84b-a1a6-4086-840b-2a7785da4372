<?php

return [
    'currency' => 'VND',

    'vnpay' => [
        'endpoint'     => env('VNPAY_ENDPOINT', 'https://sandbox.vnpayment.vn/paymentv2/vpcpay.html'),
        'tmn_code'     => env('VNPAY_TMN_CODE'),
        'hash_secret'  => env('VNPAY_HASH_SECRET'),
        'expire_minutes' => (int) env('VNPAY_EXPIRE_MINUTES', 15),
    ],

    'momo' => [
        'endpoint'     => env('MOMO_ENDPOINT', 'https://test-payment.momo.vn/v2/gateway/api'),
        'partner_code' => env('MOMO_PARTNER_CODE'),
        'access_key'   => env('MOMO_ACCESS_KEY'),
        'secret_key'   => env('MOMO_SECRET_KEY'),
        'expire_minutes' => (int) env('MOMO_EXPIRE_MINUTES', 15),
    ],
];
