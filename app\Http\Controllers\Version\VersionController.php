<?php

namespace App\Http\Controllers\Version;

use App\Helpers\FilterHelper;
use App\Enums\FilterType;
use App\Http\Controllers\Controller;
use App\Http\Requests\VersionRequest;
use App\Logics\VersionManager;
use App\Models\AppVersion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class VersionController extends Controller
{
    const PER_PAGE = 15;
    private $versionManager;
    private $filterHelper;

    /**
     * Constructor
     *
     * @param VersionManager $versionManager
     * @param FilterHelper $filterHelper
     */
    public function __construct(
        VersionManager $versionManager,
        FilterHelper $filterHelper
    ) {
        $this->versionManager = $versionManager;
        $this->filterHelper = $filterHelper;
    }

    /**
     * Display a listing of the resource.
     *
     */
    public function index(Request $request)
    {
        // Get versions with filters
        $query = $this->versionManager->getAllVersions($request);
        $latestVersions = $this->versionManager->getLatestVersions();

        // Pagination and sorting
        $perPage = $request->has('per_page') ? $request->input('per_page') : self::PER_PAGE;
        $versions = $query->paginate($perPage)->withQueryString();

        // Handle invalid page
        if ($versions->lastPage() < $request->page) {
            return redirect($request->fullUrlWithQuery(['page' => $versions->lastPage()]));
        }
        if ($request->page < 0) {
            return redirect($request->fullUrlWithQuery(['page' => 1]));
        }

        // Filter view - sử dụng enum để quản lý type
        $filterConfig = [
            FilterType::createConfig('search', FilterType::TEXT, ['prefix' => 'Search: ']),
            FilterType::createConfig('platform', FilterType::SELECT, [
                'mapping' => [
                    AppVersion::IOS => __('language.ios'),
                    AppVersion::ANDROID => __('language.android')
                ]
            ]),
            FilterType::createConfig('status', FilterType::SELECT, [
                'mapping' => [
                    AppVersion::PUBLISHED => __('language.published'),
                    AppVersion::DRAFT => __('language.draft')
                ]
            ])
        ];
        $isFilter = $this->filterHelper->renderFilterBadges($request, $filterConfig);



        return view('versions.index', [
            'versions' => $versions,
            'isFilter' => $isFilter,
            'latestVersions' => $latestVersions
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $formData = $this->prepareFormData();
        return view('versions.form', $formData);
    }
    public function edit($id)
    {
        try {
            $version = AppVersion::findOrFail($id);
            if ($version == null) {
                return redirect()
                    ->route('versions.index')
                    ->with('status_failed', __('message.version_not_found'));
            }
            $formData = $this->prepareFormData($version);
            return view('versions.form', $formData);
        } catch (\Exception $e) {
            return redirect()
                ->route('versions.index')
                ->with('status_failed', __('message.server_error'));
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param VersionRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(VersionRequest $request)
    {
        try {
            $version = $this->versionManager->createVersion($request);

            return redirect()->route('versions.index')
                ->with('status_succeed', __('message.version_create_success', [
                    'version' => $version['version'],
                    'platforms' => $version['platform_name']
                ]));
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->withInput()
                ->with([
                    'status_failed' => trans('message.server_error')
                ]);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param VersionRequest $request
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update($id, VersionRequest $request)
    {
        try {
            $version = $this->versionManager->updateVersion($id, $request);

            return redirect()->route('versions.index')
                ->with('status_succeed', __('message.version_update_success', [
                    'version' => $version['version'],
                    'platform' => $version['platform_name']
                ]));
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->withInput()
                ->with([
                    'status_failed' => trans('message.server_error')
                ]);
        }
    }

    /**
     * Prepare form data for create/edit views
     *
     * @param AppVersion|null $version
     * @return array
     */
    private function prepareFormData($version = null)
    {
        $isEdit = !is_null($version);

        return [
            'isEdit' => $isEdit,
            'version' => $version,
            'route' => $isEdit ? route('versions.update', $version->id) : route('versions.store'),
            'headerTitle' => $isEdit ? __('language.version_title_update') : __('language.version_title_create'),
            'defaultDescription' => $isEdit ? $version->description : '',
            'defaultReleaseNotes' => $isEdit ? $version->release_notes : '',
        ];
    }
}
