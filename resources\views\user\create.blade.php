@extends('layouts.master')
@section('title',trans('language.create_employee'))
@section('meta')
@stop
@section('header')
    <li class="nav-item">
        {{ trans('language.create_employee') }}
    </li>
@endsection

@section('css_library')
    @include('partials.style-library', ['datepicker' => true, 'select2' => true, 'icheck' => true])
@stop

@section('css_page')
@stop

@section('content')
    <section class="content pb-4 mt-3">
        <div class="main-breadcrumbs">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" href="{{route('home')}}"><i class="fas fa-home"></i></a>
                </li>
                <li class="nav-item">
                    {{ trans('language.employee_management') }} / {{ trans('language.create_employee') }}
                </li>
            </ul>
        </div>
        <div class="scroll-nav__profile">
            <ul>
                <li>
                    <a class="scrollTo" href="#basic_information">
                        {{ trans('language.user_basic_information') }}
                    </a>
                </li>
                <li>
                    <a class="scrollTo" href="#personal_information">
                        {{ trans('language.user_personal_information') }}
                    </a>
                </li>
                <li>
                    <a class="scrollTo" href="#employment_information">
                        {{ trans('language.user_employment_contract_details') }}
                    </a>
                </li>
            </ul>
        </div>
        <div class="container-fluid">
            @include('partials.form-user-information',[
                    'action'=>route( 'admin.user.store')
                ]
            )
        </div>
    </section>
@stop

@section('js_library')
    @include('partials.script-library', ['datepicker' => true, 'stickysidebar' => true, 'select2' => true])
@stop

@section('js_page')
<script src="{{mix('assets/pages/user/info.js')}}"></script>
<script type="text/javascript">
    $(".scrollTo").on('click', function(e) {
        e.preventDefault();
        let target = $(this).attr('href');
        $('html, body').animate({
            scrollTop: ($(target).offset().top)
        }, 1000);
    });
</script>
@stop