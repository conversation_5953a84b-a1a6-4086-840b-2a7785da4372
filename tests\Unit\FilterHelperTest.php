<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Helpers\FilterHelper;
use App\Models\AppVersion;
use Illuminate\Http\Request;
use Illuminate\Foundation\Testing\RefreshDatabase;

class FilterHelperTest extends TestCase
{
    private $filterHelper;

    protected function setUp(): void
    {
        parent::setUp();
        $this->filterHelper = new FilterHelper();
    }

    /** @test */
    public function it_can_handle_legacy_config_format()
    {
        $request = new Request([
            'id' => '1,2,3',
            'keyword' => 'test keyword',
            'email' => '<EMAIL>'
        ]);

        $fields = ['id', 'keyword', 'email'];
        $result = $this->filterHelper->renderFilterBadges($request, $fields);

        $this->assertStringContainsString('#1', $result);
        $this->assertStringContainsString('#2', $result);
        $this->assertStringContainsString('#3', $result);
        $this->assertStringContainsString('test keyword', $result);
        $this->assertStringContainsString('<EMAIL>', $result);
    }

    /** @test */
    public function it_can_handle_text_type()
    {
        $request = new Request(['keyword' => 'test keyword']);
        
        $config = [
            ['field' => 'keyword', 'type' => 'text']
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);
        
        $this->assertStringContainsString('test keyword', $result);
        $this->assertStringContainsString('badge', $result);
    }

    /** @test */
    public function it_can_handle_ids_type()
    {
        $request = new Request(['user_ids' => '1,2,3']);
        
        $config = [
            ['field' => 'user_ids', 'type' => 'ids', 'options' => ['prefix' => 'User #']]
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);
        
        $this->assertStringContainsString('User #1', $result);
        $this->assertStringContainsString('User #2', $result);
        $this->assertStringContainsString('User #3', $result);
    }

    /** @test */
    public function it_can_handle_array_type()
    {
        $request = new Request(['tags' => ['vip', 'new']]);
        
        $config = [
            ['field' => 'tags', 'type' => 'array', 'options' => [
                'mapping' => ['vip' => 'VIP Customer', 'new' => 'New Customer']
            ]]
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);
        
        $this->assertStringContainsString('VIP Customer', $result);
        $this->assertStringContainsString('New Customer', $result);
    }

    /** @test */
    public function it_can_handle_select_type()
    {
        $request = new Request(['status' => 'active']);
        
        $config = [
            ['field' => 'status', 'type' => 'select', 'options' => [
                'mapping' => ['active' => 'Đang hoạt động', 'inactive' => 'Không hoạt động']
            ]]
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);
        
        $this->assertStringContainsString('Đang hoạt động', $result);
    }

    /** @test */
    public function it_can_handle_boolean_type()
    {
        $request = new Request(['is_verified' => true]);
        
        $config = [
            ['field' => 'is_verified', 'type' => 'boolean', 'options' => [
                'labels' => ['Chưa xác thực', 'Đã xác thực']
            ]]
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);
        
        $this->assertStringContainsString('Đã xác thực', $result);
    }

    /** @test */
    public function it_can_handle_date_type()
    {
        $request = new Request(['created_date' => '2023-12-25']);
        
        $config = [
            ['field' => 'created_date', 'type' => 'date', 'options' => ['format' => 'd/m/Y']]
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);
        
        $this->assertStringContainsString('25/12/2023', $result);
    }

    /** @test */
    public function it_can_handle_custom_formatter()
    {
        $request = new Request(['special_field' => 'test value']);
        
        $config = [
            [
                'field' => 'special_field',
                'formatter' => function($value, $tagOpen, $tagClose, $options) {
                    return $tagOpen . 'Custom: ' . strtoupper($value) . $tagClose;
                }
            ]
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);
        
        $this->assertStringContainsString('Custom: TEST VALUE', $result);
    }

    /** @test */
    public function it_can_handle_custom_options()
    {
        $request = new Request(['keyword' => 'test']);
        
        $config = [
            ['field' => 'keyword', 'type' => 'text']
        ];

        $options = [
            'badge_class' => 'custom-badge',
            'wrapper_tag' => 'div',
            'prefix' => '<section>',
            'suffix' => '</section>'
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config, $options);
        
        $this->assertStringContainsString('custom-badge', $result);
        $this->assertStringContainsString('<div class="custom-badge">', $result);
        $this->assertStringContainsString('<section>', $result);
        $this->assertStringContainsString('</section>', $result);
    }

    /** @test */
    public function it_ignores_empty_fields()
    {
        $request = new Request(['keyword' => '', 'email' => null]);
        
        $config = [
            ['field' => 'keyword', 'type' => 'text'],
            ['field' => 'email', 'type' => 'text']
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);
        
        $this->assertEmpty($result);
    }

    /** @test */
    public function it_escapes_html_properly()
    {
        $request = new Request(['keyword' => '<script>alert("xss")</script>']);
        
        $config = [
            ['field' => 'keyword', 'type' => 'text']
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);
        
        $this->assertStringNotContainsString('<script>', $result);
        $this->assertStringContainsString('&lt;script&gt', $result);
    }

    /** @test */
    public function it_handles_text_max_length()
    {
        $request = new Request(['description' => 'This is a very long description that should be truncated']);
        
        $config = [
            ['field' => 'description', 'type' => 'text', 'options' => ['max_length' => 20]]
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);
        
        $this->assertStringContainsString('This is a very long ', $result);
    }

    /** @test */
    public function it_handles_range_type()
    {
        $request = new Request(['price_range' => ['100', '500']]);
        
        $config = [
            ['field' => 'price_range', 'type' => 'range', 'options' => [
                'separator' => ' - ', 'suffix' => ' USD'
            ]]
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);
        
        $this->assertStringContainsString('100 - 500 USD', $result);
    }
}
