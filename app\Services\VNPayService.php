<?php

namespace App\Services;

use App\Contracts\PaymentService;
use App\Enums\PaymentCallback;
use App\Enums\PaymentChannel;
use App\Models\Order;
use App\Models\Payment;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;

class VNPayService extends BasePaymentService implements PaymentService
{
    public function channel(): PaymentChannel
    {
        return PaymentChannel::VNPAY;
    }

    public function initiate(Order $order, array $options = []): Payment
    {
        // Reuse pending payment if still valid
        if ($existing = $this->findReusablePendingPayment($order, PaymentChannel::VNPAY)) {
            $this->log($existing, 'info', 'Reusing existing pending payment', []);
            return $existing;
        }

        // Build VNPAY meta + expiry
        $expiresAt = now()->addMinutes(Config::get('payments.vnpay.expire_minutes', 15));
        $meta = [
            'vnp_TxnRef'  => $order->code . '-' . uniqid(),
            'return_url'  => route('payments.return', ['channel' => 'vnpay']),
            'ip'          => request()->ip(),
        ];

        return $this->createPayment($order, PaymentChannel::VNPAY->value, $expiresAt, $meta);
    }

    /**
     * Build the redirect URL for VNPay payment.
     * This URL will be used to redirect the user to VNPay's payment page.
     */
    public function buildRedirectUrl(Payment $payment): string
    {
        $cfg = Config::get('payments.vnpay');
        $params = [
            'vnp_Version'   => '2.1.0',
            'vnp_Command'   => 'pay',
            'vnp_TmnCode'   => $cfg['tmn_code'],
            'vnp_Amount'    => $payment->amount * 100,
            'vnp_CreateDate' => now()->format('YmdHis'),
            'vnp_CurrCode'  => 'VND',
            'vnp_IpAddr'    => $payment->meta['ip'] ?? request()->ip(),
            'vnp_Locale'    => 'vn',
            'vnp_OrderInfo' => 'Payment for order ' . $payment->order->code,
            'vnp_OrderType' => 'billpayment',
            'vnp_ReturnUrl' => $payment->meta['return_url'],
            'vnp_TxnRef'    => $payment->meta['vnp_TxnRef'],
            'vnp_ExpireDate' => $payment->expires_at?->format('YmdHis'),
        ];
        ksort($params);
        $hashData = http_build_query($params);
        $vnpSecureHash = hash_hmac('sha512', $hashData, $cfg['hash_secret']);

        $payUrl = $cfg['endpoint'] . '?' . http_build_query(array_merge($params, [
            'vnp_SecureHash' => $vnpSecureHash
        ]));
        
        Log::debug('VNPay redirect URL', [
            'url' => $payUrl,
            'input_data' => $params,
            'secure_hash' => $vnpSecureHash,
        ]);

        return $payUrl;
    }

    /**
     * Handle the return callback from VNPay after payment.
     * This is typically used for synchronous callbacks.
     */
    public function handleReturn(Request $request): Payment
    {
        return $this->handleCallback($request, PaymentCallback::RETURN->value);
    }

    /**
     * Handle the IPN (Instant Payment Notification) callback from VNPay.
     * This is used for asynchronous notifications about payment status.
     */
    public function handleIpn(Request $request): Payment
    {
        return $this->handleCallback($request, PaymentCallback::IPN->value);
    }

    /**
     * Common logic for handling both return and IPN callbacks.
     * It verifies the signature, checks the payment status, and updates the payment record accordingly.
     */
    private function handleCallback(Request $request, string $event): Payment {
        $payment = $this->findPaymentByTxnRef((string)$request->input('vnp_TxnRef'));
        $valid   = $this->verifySignature($request->all());
        $payload = $request->all() + ['signature_valid' => $valid, 'source' => $event];

        // Capture provider transaction id if present
        $providerTxnId = (string)($request->input('vnp_TransactionNo') ?? $request->input('vnp_TransactionNo'));

        // Idempotency by provider transaction id
        $payment = $this->ensureIdempotentByProviderTxn($providerTxnId, $payment);

        if (!$valid) {
            return $this->markFailed($payment, 'INVALID_SIGN', "Invalid signature ($event)", $payload, $event);
        }

        // Mark SUCCESS if provider says success.
        if ($this->isSuccessCode($request)) {
            return $this->markSuccess($payment, ['code' => '00', 'message' => 'Success'] + $payload, 
                $providerTxnId, $event);
        }

        // Non-success → FAILED (keep order pending for retry)
        return $this->markFailed($payment, (string)$request->input('vnp_ResponseCode'), "VNPay failed ($event)",
            $payload, $event);
    }

    /**
     * Query the payment status from VNPay.
     * This is typically used when the IPN callback is missing or to reconcile payment status.
     */
    public function query(Payment $payment): Payment
    {
        // Optional: call VNPay query API to reconcile when IPN missing
        // If confirmed success -> markSuccess(); if confirmed fail -> markFailed()
        return $payment->refresh();
    }

    /**
     * Verify the signature from VNPay callback.
     * This ensures the data integrity and authenticity of the callback.
     */
    protected function verifySignature(array $params): bool
    {
        $secure = Arr::pull($params, 'vnp_SecureHash', null);
        $cfg    = config('payments.vnpay');
        ksort($params);
        $hashData = http_build_query($params);
        $calc = hash_hmac('sha512', $hashData, $cfg['hash_secret']);
        return hash_equals($calc, (string)$secure);
    }

    /**
     * Check if the response code indicates a successful transaction.
     * VNPay uses '00' for both response code and transaction status to indicate success.
     */
    protected function isSuccessCode(Request $request): bool
    {
        $respCode = (string)$request->input('vnp_ResponseCode');      // 00 => success
        $txnStat  = (string)$request->input('vnp_TransactionStatus'); // 00 => success
        return $respCode === '00' && $txnStat === '00';
    }

    /**
     * Find a payment by its VNPay transaction reference.
     * This is used to retrieve the payment record based on the transaction reference provided by VNPay.
     */
    protected function findPaymentByTxnRef(string $vnp_TxnRef): Payment
    {
        $payment = Payment::whereJsonContains('meta->vnp_TxnRef', $vnp_TxnRef)
            ->latest()->first();

        if (!$payment) {
            throw new ModelNotFoundException("Payment not found by vnp_TxnRef={$vnp_TxnRef}");
        }
        return $payment;
    }

    /**
     * Get a human-readable message for VNPay response codes.
     * This maps VNPay's response codes to user-friendly messages.
     */
    public function getResponseCodeMessage(?string $code): ?string
    {
        $map = [
            '00' => 'Transaction success',
            '07' => 'Debited but suspected fraud',
            '09' => 'Card/account not registered for InternetBanking',
            '10' => 'Authentication failed too many times',
            '11' => 'Payment window expired',
            '12' => 'Card/account locked',
            '13' => 'OTP verification failed',
            '15' => 'Transaction expired',
            '24' => 'Customer canceled the transaction',
            '51' => 'Insufficient balance',
            '65' => 'Exceeded daily transaction limit',
            '75' => 'Acquirer/bank under maintenance',
            '79' => 'Payment password entered too many times',
            '99' => 'Other errors',
        ];
        return $map[$code] ?? 'Unmapped VNPay code';
    }
}
