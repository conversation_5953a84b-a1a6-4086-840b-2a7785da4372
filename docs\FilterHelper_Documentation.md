# FilterHelper Documentation

## Tổng quan

FilterHelper đã được cải tiến để trở nên linh hoạt và có thể tái sử dụng cao hơn. Thay vì hard-code logic cho từng field cụ thể, bây giờ bạn có thể cấu hình các field với các kiểu dữ liệu và formatter khác nhau.

## Các kiểu dữ liệu được hỗ trợ

### 1. Text (`text`)
- **Mô tả**: Hiển thị text đơn giản
- **Options**:
  - `prefix`: Tiền tố (mặc định: '')
  - `suffix`: Hậu tố (mặc định: '')
  - `max_length`: Độ dài tối đa, sẽ cắt và thêm '...' (mặc định: null)

```php
['field' => 'keyword', 'type' => 'text', 'options' => ['max_length' => 20]]
```

### 2. IDs (`id`, `ids`)
- **<PERSON><PERSON> tả**: Hi<PERSON><PERSON> thị danh sách ID được phân tách bằng dấu phẩy
- **Options**:
  - `prefix`: Tiền tố cho mỗi ID (mặc định: '#')
  - `separator`: Ký tự phân tách (mặc định: ',')

```php
['field' => 'user_ids', 'type' => 'ids', 'options' => ['prefix' => 'User #']]
```

### 3. Array/Multiple (`array`, `multiple`)
- **Mô tả**: Hiển thị các giá trị từ mảng
- **Options**:
  - `mapping`: Mảng ánh xạ giá trị -> nhãn hiển thị

```php
['field' => 'tags', 'type' => 'array', 'options' => [
    'mapping' => ['vip' => 'VIP Customer', 'new' => 'New Customer']
]]
```

### 4. Select/Enum (`select`, `enum`)
- **Mô tả**: Hiển thị giá trị đã được ánh xạ từ select/enum
- **Options**:
  - `mapping`: Mảng ánh xạ giá trị -> nhãn hiển thị

```php
['field' => 'status', 'type' => 'select', 'options' => [
    'mapping' => ['active' => 'Đang hoạt động', 'inactive' => 'Không hoạt động']
]]
```

### 5. Boolean (`boolean`)
- **Mô tả**: Hiển thị giá trị boolean
- **Options**:
  - `labels`: Mảng [false_label, true_label] (mặc định: ['No', 'Yes'])

```php
['field' => 'is_verified', 'type' => 'boolean', 'options' => [
    'labels' => ['Chưa xác thực', 'Đã xác thực']
]]
```

### 6. Date (`date`)
- **Mô tả**: Hiển thị ngày tháng với format tùy chỉnh
- **Options**:
  - `format`: Format ngày tháng (mặc định: 'd/m/Y')

```php
['field' => 'created_date', 'type' => 'date', 'options' => ['format' => 'd/m/Y H:i']]
```

### 7. Range (`range`)
- **Mô tả**: Hiển thị khoảng giá trị (from-to)
- **Options**:
  - `separator`: Ký tự phân tách (mặc định: ' - ')
  - `prefix`: Tiền tố
  - `suffix`: Hậu tố

```php
['field' => 'price_range', 'type' => 'range', 'options' => [
    'separator' => ' đến ', 'suffix' => ' VNĐ'
]]
```

## Cách sử dụng

### 1. Cách cũ (Backward Compatibility)
```php
// Vẫn hoạt động như trước
$fields = ['id', 'keyword', 'email', 'phone', 'gender'];
$isFilter = $this->filterHelper->renderFilterBadges($request, $fields);
```

### 2. Cấu hình cơ bản
```php
$config = [
    ['field' => 'id', 'type' => 'ids'],
    ['field' => 'keyword', 'type' => 'text'],
    ['field' => 'status', 'type' => 'select', 'options' => [
        'mapping' => ['active' => 'Active', 'inactive' => 'Inactive']
    ]]
];

$isFilter = $this->filterHelper->renderFilterBadges($request, $config);
```

### 3. Cấu hình với options
```php
$config = [
    // ... cấu hình fields
];

$options = [
    'badge_class' => 'badge badge-info badge-filter',
    'wrapper_tag' => 'span',
    'separator' => ' ',
    'prefix' => '<div class="filter-badges">',
    'suffix' => '</div>'
];

$isFilter = $this->filterHelper->renderFilterBadges($request, $config, $options);
```

### 4. Custom Formatter
```php
$config = [
    [
        'field' => 'user_id',
        'formatter' => function($value, $tagOpen, $tagClose, $options) {
            $user = User::find($value);
            $displayValue = $user ? $user->name : "Unknown User";
            return $tagOpen . $displayValue . $tagClose;
        }
    ]
];
```

## Options cho renderFilterBadges

- `badge_class`: CSS class cho badge (mặc định: 'badge badge-primary badge-filter bgr')
- `wrapper_tag`: HTML tag bao quanh (mặc định: 'span')
- `separator`: Ký tự phân tách giữa các badge (mặc định: '')
- `prefix`: HTML prefix cho toàn bộ output (mặc định: '')
- `suffix`: HTML suffix cho toàn bộ output (mặc định: '')

## Ví dụ thực tế

### User Controller
```php
$config = [
    ['field' => 'id', 'type' => 'ids', 'options' => ['prefix' => '#']],
    ['field' => 'keyword', 'type' => 'text', 'options' => ['max_length' => 20]],
    ['field' => 'email', 'type' => 'text'],
    ['field' => 'phone', 'type' => 'text'],
    ['field' => 'gender', 'type' => 'array', 'options' => [
        'mapping' => trans('language.genders')
    ]]
];
```

### Version Controller
```php
$config = [
    ['field' => 'search', 'type' => 'text', 'options' => ['prefix' => 'Search: ']],
    ['field' => 'platform', 'type' => 'select', 'options' => [
        'mapping' => [
            AppVersion::IOS => __('language.ios'),
            AppVersion::ANDROID => __('language.android')
        ]
    ]],
    ['field' => 'status', 'type' => 'select', 'options' => [
        'mapping' => [
            AppVersion::PUBLISHED => __('language.published'),
            AppVersion::DRAFT => __('language.draft')
        ]
    ]]
];
```

## Lợi ích của cách tiếp cận mới

1. **Tính linh hoạt cao**: Có thể cấu hình bất kỳ field nào mà không cần sửa code
2. **Tái sử dụng**: Một lần viết, sử dụng ở nhiều nơi
3. **Mở rộng dễ dàng**: Thêm type mới hoặc custom formatter
4. **Backward compatibility**: Code cũ vẫn hoạt động
5. **Type safety**: Mỗi kiểu dữ liệu có logic xử lý riêng
6. **Customizable**: Có thể tùy chỉnh HTML output, CSS class, v.v.
