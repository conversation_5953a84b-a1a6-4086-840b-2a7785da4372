<?php

namespace App\Enums;

/**
 * Enum cho các kiểu field trong FilterHelper
 * Quản lý tất cả các case có thể sử dụng trong switch
 */
enum FilterFieldType: string
{
    case TEXT = 'text';
    case IDS = 'ids';
    case ARRAY = 'array';
    case SELECT = 'select';
    case BOOLEAN = 'boolean';
    case DATE = 'date';
    case RANGE = 'range';

    /**
     * L<PERSON>y tất cả các type có sẵn
     */
    public static function getAllTypes(): array
    {
        return array_map(fn($case) => $case->value, self::cases());
    }

    /**
     * Kiểm tra type có hợp lệ không
     */
    public static function isValid(string $type): bool
    {
        return in_array($type, self::getAllTypes());
    }

    /**
     * Tạo field config nhanh
     */
    public static function createConfig(string $field, FilterFieldType $type, array $options = []): array
    {
        return [
            'field' => $field,
            'type' => $type->value,
            'options' => $options
        ];
    }

    /**
     * <PERSON><PERSON><PERSON> mô tả cho từng type
     */
    public function getDescription(): string
    {
        return match ($this) {
            self::TEXT => 'Text đơn giản, mặc định không có max_length',
            self::IDS => 'Danh sách ID phân tách bằng dấu phẩy với prefix',
            self::ARRAY => 'Mảng giá trị với mapping tùy chọn',
            self::SELECT => 'Giá trị select/enum với mapping',
            self::BOOLEAN => 'Giá trị true/false với nhãn tùy chỉnh',
            self::DATE => 'Ngày tháng với format tùy chỉnh',
            self::RANGE => 'Khoảng giá trị (from-to)',
        };
    }
}
