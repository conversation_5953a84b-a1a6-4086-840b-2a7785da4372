<?php

namespace App\Helpers;



class FilterHelper
{
    /**
     * Render filter badges HTML
     * @param \Illuminate\Http\Request $request
     * @param array $config - C<PERSON>u hình: [['field' => 'name', 'type' => 'text', 'options' => [...]]]
     * @param array $options - T<PERSON>y chọn hiển thị
     * @return string
     */
    public function renderFilterBadges($request, array $config, array $options = []): string
    {
        $defaultOptions = [
            'badge_class' => 'badge badge-primary badge-filter bgr',
            'wrapper_tag' => 'span',
            'separator' => '',
            'prefix' => '',
            'suffix' => ''
        ];

        $options = array_merge($defaultOptions, $options);
        $tagOpen = "<{$options['wrapper_tag']} class=\"{$options['badge_class']}\">";
        $tagClose = "</{$options['wrapper_tag']}>";
        $output = '';

        foreach ($config as $fieldConfig) {
            $field = $fieldConfig['field'];
            $type = $fieldConfig['type'] ?? 'text'; // Mặc định là text
            $fieldOptions = $fieldConfig['options'] ?? [];

            if ($request->has($field) && $request->$field != null && $request->$field !== '') {
                $value = $this->processFieldByType($request->$field, $type, $tagOpen, $tagClose, $fieldOptions);
                if ($value) {
                    $output .= $options['separator'] . $value;
                }
            }
        }

        return $options['prefix'] . $output . $options['suffix'];
    }



    /**
     * Xử lý field theo type
     */
    private function processFieldByType($value, $type, $tagOpen, $tagClose, $options = [])
    {
        switch ($type) {
            case 'id':
            case 'ids':
                return $this->formatIds($value, $tagOpen, $tagClose, $options);

            case 'array':
            case 'multiple':
                return $this->formatArray($value, $tagOpen, $tagClose, $options);

            case 'select':
            case 'enum':
                return $this->formatSelect($value, $tagOpen, $tagClose, $options);

            case 'boolean':
                return $this->formatBoolean($value, $tagOpen, $tagClose, $options);

            case 'date':
                return $this->formatDate($value, $tagOpen, $tagClose, $options);

            case 'range':
                return $this->formatRange($value, $tagOpen, $tagClose, $options);

            case 'text':
            default:
                return $this->formatText($value, $tagOpen, $tagClose, $options);
        }
    }

    /**
     * Format IDs (comma separated)
     */
    private function formatIds($value, $tagOpen, $tagClose, $options)
    {
        $prefix = $options['prefix'] ?? '#';
        $separator = $options['separator'] ?? ',';

        $ids = array_filter(array_map('trim', explode($separator, StringHelper::escapeHtml($value))));
        $output = '';

        foreach ($ids as $id) {
            $output .= $tagOpen . $prefix . $id . $tagClose;
        }

        return $output;
    }

    /**
     * Format array values
     */
    private function formatArray($value, $tagOpen, $tagClose, $options)
    {
        if (!is_array($value)) {
            return '';
        }

        $output = '';
        $mapping = $options['mapping'] ?? [];

        foreach ($value as $item) {
            $displayValue = isset($mapping[$item]) ? $mapping[$item] : $item;
            $output .= $tagOpen . StringHelper::escapeHtml($displayValue) . $tagClose;
        }

        return $output;
    }

    /**
     * Format select/enum values
     */
    private function formatSelect($value, $tagOpen, $tagClose, $options)
    {
        $mapping = $options['mapping'] ?? [];
        $displayValue = isset($mapping[$value]) ? $mapping[$value] : $value;

        return $tagOpen . StringHelper::escapeHtml($displayValue) . $tagClose;
    }

    /**
     * Format boolean values
     */
    private function formatBoolean($value, $tagOpen, $tagClose, $options)
    {
        $labels = $options['labels'] ?? ['No', 'Yes'];
        $displayValue = $value ? $labels[1] : $labels[0];

        return $tagOpen . StringHelper::escapeHtml($displayValue) . $tagClose;
    }

    /**
     * Format date values
     */
    private function formatDate($value, $tagOpen, $tagClose, $options)
    {
        $format = $options['format'] ?? 'd/m/Y';
        $displayValue = date($format, strtotime($value));

        return $tagOpen . StringHelper::escapeHtml($displayValue) . $tagClose;
    }

    /**
     * Format range values (from-to)
     */
    private function formatRange($value, $tagOpen, $tagClose, $options)
    {
        $separator = $options['separator'] ?? ' - ';
        $prefix = $options['prefix'] ?? '';
        $suffix = $options['suffix'] ?? '';

        if (is_array($value) && count($value) == 2) {
            $displayValue = $prefix . $value[0] . $separator . $value[1] . $suffix;
        } else {
            $displayValue = $prefix . $value . $suffix;
        }

        return $tagOpen . StringHelper::escapeHtml($displayValue) . $tagClose;
    }

    /**
     * Format text values
     */
    private function formatText($value, $tagOpen, $tagClose, $options)
    {
        $prefix = $options['prefix'] ?? '';
        $suffix = $options['suffix'] ?? '';
        $maxLength = $options['max_length'] ?? null;

        $displayValue = $prefix . $value . $suffix;

        if ($maxLength && strlen($displayValue) > $maxLength) {
            $displayValue = substr($displayValue, 0, $maxLength) . '...';
        }

        return $tagOpen . StringHelper::escapeHtml($displayValue) . $tagClose;
    }

}
