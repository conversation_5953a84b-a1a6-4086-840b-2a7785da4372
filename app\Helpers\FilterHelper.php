<?php

namespace App\Helpers;

use App\Models\AppVersion;

class FilterHelper
{
    /**
     * Render filter badges HTML
     * @param \Illuminate\Http\Request $request
     * @param array $fields
     * @param array $customs (các field đặc biệt cần x<PERSON> lý riêng, vd: gender)
     * @param string $badgeClass
     * @return string
     */
    public function renderFilterBadges($request, $fields)
    {
        $tagSpanOpen = '<span class="badge badge-primary badge-filter bgr">';
        $tagSpanClose = '</span>';
        $output = '';
        foreach ($fields as $field) {
            $value = '';
            if ($request->has($field) && $request->$field != null) {
                switch ($field) {
                    case 'id':
                        $idItems = array_filter(array_map("trim", explode(",",
                            StringHelper::escapeHtml($request->id))));
                        if (!empty($idItems)) {
                            foreach ($idItems as $idItem) {
                                $value .= $tagSpanOpen . "#" . $idItem . $tagSpanClose;
                            }
                        }
                        break;
                    case 'gender':
                        foreach ($request->gender as $gender) {
                            $value .= $tagSpanOpen . trans('language.genders')[$gender] . $tagSpanClose;
                        }
                        break;
                    case 'platform':
                        $platform = $request->platform;
                        $platformName = $platform == AppVersion::IOS ? __('language.ios') : __('language.android');
                        $value = $tagSpanOpen.StringHelper::escapeHtml($platformName) . $tagSpanClose;
                        break;
                    case 'status':
                        $status = $request->status;
                        $statusName = $status == AppVersion::PUBLISHED ? __('language.published') : __('language.draft');
                        $value = $tagSpanOpen.StringHelper::escapeHtml($statusName) . $tagSpanClose;
                        break;
                    default:
                        $value = $tagSpanOpen . StringHelper::escapeHtml($request->$field) . $tagSpanClose;
                        break;
                }
                $output .= $value;
            }
        }

        return $output;
    }
}
