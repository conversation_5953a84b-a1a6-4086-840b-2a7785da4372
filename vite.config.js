import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';

export default defineConfig({
    server: {
        host: '0.0.0.0',
        port: 5173,
        strictPort: true,
        hmr: {
            host: 'beefarm_be.local',
            protocol: 'ws',
            port: 5173
        },
        cors: {
            origin: [
                'http://beefarm_be.local',
                'http://beefarm_be.local:8000',
                'http://beefarm_be.local:5173',
                'http://localhost:8000',
                'http://localhost:5173'
            ],
            methods: ['GET', 'HEAD', 'PUT', 'PATCH', 'POST', 'DELETE'],
            allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
            credentials: true
        }
    },
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/js/app.js',
                // Common
                'resources/build/js/AdminLTE.js',
                'resources/build/scss/adminlte.scss',
                'resources/build/js/common.js',
                'resources/build/scss/common.scss'
            ],
            refresh: true,
        }),
    ],
});
