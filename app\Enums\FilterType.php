<?php

namespace App\Enums;

/**
 * Enum quản lý các kiểu field trong FilterHelper
 * Thay thế việc gõ text để tránh lỗi typo
 */
enum FilterType: string
{
    case TEXT = 'text';
    case IDS = 'ids';
    case ARRAY = 'array';
    case SELECT = 'select';
    case BOOLEAN = 'boolean';
    case DATE = 'date';
    case RANGE = 'range';

    /**
     * Lấy tất cả các type có sẵn
     */
    public static function getAllTypes(): array
    {
        return array_map(fn($case) => $case->value, self::cases());
    }

    /**
     * <PERSON>ểm tra type có hợp lệ không
     */
    public static function isValid(string $type): bool
    {
        return in_array($type, self::getAllTypes());
    }

    /**
     * Tạo field config nhanh với enum
     */
    public static function createConfig(string $field, FilterType $type, array $options = []): array
    {
        return [
            'field' => $field,
            'type' => $type->value,
            'options' => $options
        ];
    }

    /**
     * <PERSON><PERSON>y mô tả cho từng type
     */
    public function getDescription(): string
    {
        return match ($this) {
            self::TEXT => 'Text đơn giản với tùy chọn max_length, prefix, suffix',
            self::IDS => 'Danh sách ID phân tách bằng dấu phẩy với prefix',
            self::ARRAY => 'Mảng giá trị với mapping tùy chọn',
            self::SELECT => 'Giá trị select/enum với mapping',
            self::BOOLEAN => 'Giá trị true/false với nhãn tùy chỉnh',
            self::DATE => 'Ngày tháng với format tùy chỉnh',
            self::RANGE => 'Khoảng giá trị (from-to)',
        };
    }

    /**
     * Lấy default options cho từng type
     */
    public function getDefaultOptions(): array
    {
        return match ($this) {
            self::TEXT => [
                'prefix' => '',
                'suffix' => '',
                'max_length' => null
            ],
            self::IDS => [
                'prefix' => '#',
                'separator' => ','
            ],
            self::ARRAY => [
                'mapping' => []
            ],
            self::SELECT => [
                'mapping' => []
            ],
            self::BOOLEAN => [
                'labels' => ['No', 'Yes']
            ],
            self::DATE => [
                'format' => 'd/m/Y'
            ],
            self::RANGE => [
                'separator' => ' - ',
                'prefix' => '',
                'suffix' => ''
            ],
        };
    }
}
