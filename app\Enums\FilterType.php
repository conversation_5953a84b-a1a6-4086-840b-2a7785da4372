<?php

namespace App\Enums;

/**
 * Enum đơn giản quản lý các kiểu field trong FilterHelper
 */
enum FilterType: string
{
    case TEXT = 'text';
    case IDS = 'ids';
    case ARRAY = 'array';
    case SELECT = 'select';
    case BOOLEAN = 'boolean';
    case DATE = 'date';
    case RANGE = 'range';

    /**
     * <PERSON><PERSON>y tất cả các type có sẵn
     */
    public static function getAllTypes(): array
    {
        return array_map(fn($case) => $case->value, self::cases());
    }

    /**
     * Kiểm tra type có hợp lệ không
     */
    public static function isValid(string $type): bool
    {
        return in_array($type, self::getAllTypes());
    }
}
