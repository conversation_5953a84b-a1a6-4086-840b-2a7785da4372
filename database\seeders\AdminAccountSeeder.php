<?php

namespace Database\Seeders;

use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminAccountSeeder extends Seeder
{
    public function run(): void
    {
        if (User::where('email', '<EMAIL>')->exists()) {
            return;
        }

        $admin = User::create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('a123123'),
            'email_verified_at' => now(),
        ]);

        $admin->syncRoles(Role::all());
    }
}
