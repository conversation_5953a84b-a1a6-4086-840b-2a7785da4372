<?php

use App\Http\Controllers\AddressController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\Version\VersionController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // User management routes
    Route::resource('user', UserController::class);
    Route::group(['prefix' => 'user'], function () {
        Route::get('{id}/avatar', [UserController::class, 'getAvatar'])->name('user.avatar');
    });

    // Get administrative units
    Route::get('getCommuneList', [AddressController::class, 'getCommuneList'])->name('getCommuneList');

    // Payment
    Route::post('/payments/start', [PaymentController::class, 'start'])->name('payments.start');

    //Version
    Route::prefix('versions')->as('versions.')->group(function () {
        Route::get('/', [VersionController::class, 'index'])->middleware('session')->name('index');
        Route::get('/create', [VersionController::class, 'create'])->name('create');
        Route::post('/store', [VersionController::class, 'store'])->name('store');
        Route::get('/edit/{id}', [VersionController::class, 'edit'])->name('edit');
        Route::put('/update/{id}', [VersionController::class, 'update'])->name('update');
    });
});

// Payment
Route::get('/payments/return/{channel}', [PaymentController::class, 'return'])->name('payments.return');
Route::match(['GET','POST'], '/payments/ipn/{channel}', [PaymentController::class, 'ipn'])->name('payments.ipn');
Route::get('/payments/result/{payment}', [PaymentController::class, 'result'])->name('payments.result');

require __DIR__.'/auth.php';
