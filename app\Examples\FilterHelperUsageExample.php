<?php

namespace App\Examples;

use App\Helpers\FilterHelper;
use App\Models\AppVersion;
use Illuminate\Http\Request;

/**
 * <PERSON><PERSON> dụ sử dụng FilterHelper mới với các cách cấu hình khác nhau
 */
class FilterHelperUsageExample
{
    private $filterHelper;

    public function __construct(FilterHelper $filterHelper)
    {
        $this->filterHelper = $filterHelper;
    }

    /**
     * Ví dụ 1: Sử dụng cách cũ (backward compatibility)
     */
    public function legacyUsage(Request $request)
    {
        // Cách cũ vẫn hoạt động
        $fields = ['id', 'keyword', 'email', 'phone', 'gender'];
        $isFilter = $this->filterHelper->renderFilterBadges($request, $fields);
        
        return $isFilter;
    }

    /**
     * Ví dụ 2: Cấu hình cơ bản với type
     */
    public function basicConfigUsage(Request $request)
    {
        $config = [
            ['field' => 'id', 'type' => 'ids'],
            ['field' => 'keyword', 'type' => 'text'],
            ['field' => 'email', 'type' => 'text'],
            ['field' => 'status', 'type' => 'select', 'options' => [
                'mapping' => [
                    'active' => 'Đang hoạt động',
                    'inactive' => 'Không hoạt động'
                ]
            ]]
        ];

        $isFilter = $this->filterHelper->renderFilterBadges($request, $config);
        return $isFilter;
    }

    /**
     * Ví dụ 3: Cấu hình nâng cao với custom formatter
     */
    public function advancedConfigUsage(Request $request)
    {
        $config = [
            [
                'field' => 'user_id',
                'type' => 'ids',
                'options' => ['prefix' => 'User #']
            ],
            [
                'field' => 'tags',
                'type' => 'array',
                'options' => [
                    'mapping' => [
                        'vip' => 'VIP Customer',
                        'new' => 'New Customer',
                        'loyal' => 'Loyal Customer'
                    ]
                ]
            ],
            [
                'field' => 'created_date',
                'type' => 'date',
                'options' => ['format' => 'd/m/Y']
            ],
            [
                'field' => 'price_range',
                'type' => 'range',
                'options' => [
                    'separator' => ' đến ',
                    'prefix' => '',
                    'suffix' => ' VNĐ'
                ]
            ],
            [
                'field' => 'is_verified',
                'type' => 'boolean',
                'options' => [
                    'labels' => ['Chưa xác thực', 'Đã xác thực']
                ]
            ]
        ];

        $options = [
            'badge_class' => 'badge badge-info badge-filter',
            'separator' => ' ',
            'prefix' => '<div class="filter-badges">',
            'suffix' => '</div>'
        ];

        $isFilter = $this->filterHelper->renderFilterBadges($request, $config, $options);
        return $isFilter;
    }

    /**
     * Ví dụ 4: Sử dụng custom formatter
     */
    public function customFormatterUsage(Request $request)
    {
        $config = [
            [
                'field' => 'special_field',
                'formatter' => function($value, $tagOpen, $tagClose, $options) {
                    // Custom logic để format giá trị
                    $displayValue = "Custom: " . strtoupper($value);
                    return $tagOpen . $displayValue . $tagClose;
                }
            ],
            [
                'field' => 'user_info',
                'formatter' => function($value, $tagOpen, $tagClose, $options) {
                    // Có thể query database hoặc xử lý phức tạp
                    $user = \App\Models\User::find($value);
                    $displayValue = $user ? $user->name : "Unknown User";
                    return $tagOpen . $displayValue . $tagClose;
                }
            ]
        ];

        $isFilter = $this->filterHelper->renderFilterBadges($request, $config);
        return $isFilter;
    }

    /**
     * Ví dụ 5: Cấu hình cho User Controller
     */
    public function userControllerConfig(Request $request)
    {
        $config = [
            [
                'field' => 'id',
                'type' => 'ids',
                'options' => ['prefix' => '#']
            ],
            [
                'field' => 'keyword',
                'type' => 'text',
                'options' => ['max_length' => 20]
            ],
            [
                'field' => 'email',
                'type' => 'text'
            ],
            [
                'field' => 'phone',
                'type' => 'text'
            ],
            [
                'field' => 'gender',
                'type' => 'array',
                'options' => ['mapping' => trans('language.genders')]
            ]
        ];

        return $this->filterHelper->renderFilterBadges($request, $config);
    }

    /**
     * Ví dụ 6: Cấu hình cho Version Controller
     */
    public function versionControllerConfig(Request $request)
    {
        $config = [
            [
                'field' => 'search',
                'type' => 'text',
                'options' => ['prefix' => 'Search: ']
            ],
            [
                'field' => 'platform',
                'type' => 'select',
                'options' => [
                    'mapping' => [
                        AppVersion::IOS => __('language.ios'),
                        AppVersion::ANDROID => __('language.android')
                    ]
                ]
            ],
            [
                'field' => 'status',
                'type' => 'select',
                'options' => [
                    'mapping' => [
                        AppVersion::PUBLISHED => __('language.published'),
                        AppVersion::DRAFT => __('language.draft')
                    ]
                ]
            ]
        ];

        return $this->filterHelper->renderFilterBadges($request, $config);
    }

    /**
     * Ví dụ 7: Cấu hình với nhiều kiểu dữ liệu khác nhau
     */
    public function comprehensiveExample(Request $request)
    {
        $config = [
            // Text fields
            ['field' => 'name', 'type' => 'text'],
            ['field' => 'description', 'type' => 'text', 'options' => ['max_length' => 30]],
            
            // ID fields
            ['field' => 'ids', 'type' => 'ids', 'options' => ['prefix' => '#', 'separator' => ',']],
            
            // Select/Enum fields
            ['field' => 'category', 'type' => 'select', 'options' => [
                'mapping' => ['tech' => 'Technology', 'business' => 'Business']
            ]],
            
            // Array/Multiple fields
            ['field' => 'tags', 'type' => 'array', 'options' => [
                'mapping' => ['urgent' => 'Urgent', 'important' => 'Important']
            ]],
            
            // Boolean fields
            ['field' => 'is_active', 'type' => 'boolean', 'options' => [
                'labels' => ['Inactive', 'Active']
            ]],
            
            // Date fields
            ['field' => 'start_date', 'type' => 'date', 'options' => ['format' => 'd/m/Y']],
            
            // Range fields
            ['field' => 'price_range', 'type' => 'range', 'options' => [
                'separator' => ' - ', 'suffix' => '$'
            ]],
            
            // Custom formatter
            ['field' => 'complex_field', 'formatter' => function($value, $tagOpen, $tagClose, $options) {
                return $tagOpen . "Complex: " . json_encode($value) . $tagClose;
            }]
        ];

        $options = [
            'badge_class' => 'badge badge-secondary badge-filter',
            'wrapper_tag' => 'span',
            'separator' => ' ',
            'prefix' => '<div class="filter-container">',
            'suffix' => '</div>'
        ];

        return $this->filterHelper->renderFilterBadges($request, $config, $options);
    }
}
