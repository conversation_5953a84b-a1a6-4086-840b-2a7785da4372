@import "variable";
@import "main-header";
@import "main-sidebar";
@import "tag-user";
@import "form-image";
@import "table";
@import "avatar-list";
@import "title-tree";
@import "text-format";
@import "table-response-block";
@import "no-data-found";
@import "timepicker";
@import "project-document";
@import "attachment";
@import "form";
@import "form-select";
@import "profile-user";
@import "grantt";
input[type="time"]::-webkit-calendar-picker-indicator {
    background: none;
}

input[type=time]::-webkit-datetime-edit-ampm-field {
    display: none;
}

.breadcrumb-item+.breadcrumb-item {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px;
}

.markdown-body h3 {
    font-size: 1.5em !important;
}

.markdown-body h1 {
    font-size: 2.25em !important;
}

h1 {
    font-size: 1.5rem !important;
}

h3 {
    font-size: 1rem !important;
}

#modalCompareWiki h1 {
    font-size: 2.25em !important;
}

#modalCompareWiki h2 {
    font-size: 1.75em !important;
}

#modalCompareWiki h3 {
    font-size: 1.5em !important;
}

#modalCompareWiki h4 {
    font-size: 1.25em !important;
}

#modalCompareWiki h5 {
    font-size: 1em !important;
}

#modalCompareWiki h6 {
    font-size: 1em !important;
    color: #777;
}

.nav-tabs.nav-tabs-menu {
    font-size: 11px !important;
}

.select2-selection {
    border-radius: 0 !important;
}

.balge-color {
    color: white !important;
}

a {
    color: #005fc5;
    font-size: 14px;
}

a:hover {
    color: #005fc5;
}

.form-control {
    border-radius: 0;
}

.input-group-append {
    .input-group-text {
        // background: none;
        border-radius: 0;
        width: 48px;
        justify-content: center;
    }
}

.bg-danger {
    background-color: #FFDFE0 !important;
    color: black !important;
}
.btn-delete-jb{
    color: #7C7C7C  !important;
}
.btn-delete-jb.btn-outline-danger {
    border-color: #7C7C7C;
}

.btn-delete-jb.btn-outline-danger:hover {
    color: #7C7C7C  !important;
}

a.text-danger.btn-delete-jb:hover,
a.text-danger.btn-delete-jb:focus {
    color: #7C7C7C  !important;
}

.card-body {
    .note-toolbar {
        background-color: rgba(255, 255, 255, 0);
    }
}

.page-item.active .page-link {
    background-color: #c1c1c1;
    border-color: #c1c1c1;
}

.page-link {
    color: black;
}

.page-link:hover {
    color: black;
}

.prevent-overflow-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.content-wrapper {
    background: #F5F5F5 !important;
}

.content-wrapper .container-fluid {
    padding-left: 0;
    padding-right: 0;
}

.user-short__info {
    text-align: center;
    font-size: 16px;
    a,
    .blue {
        color: #0791A3;
    }
    p:not(:first-child) {
        margin-bottom: 5px;
    }
}

.card {
    &.card-profile {
        border: 0;
        background: transparent;
        box-shadow: none;
        .card-header:not(.note-toolbar) {
            background: transparent;
            border-bottom: 0;
            text-align: left;
            font: normal normal normal 22px / 54px Noto Sans JP;
            letter-spacing: 0;
            color: #464A53;
            opacity: 1;
        }
        .card-body {
            background: #FFFFFF 0% 0% no-repeat padding-box;
            box-shadow: 0.47px 3px 10px #7777771A;
            border-radius: 12px;
        }
    }
}

.card {
    .card-body {
        background: #FFFFFF 0% 0% no-repeat padding-box;
        box-shadow: 0.47px 3px 10px #7777771A;
        border-radius: 12px;
    }
}

.main-breadcrumbs {
    color: #0791A3;
    font-size: 14px;
    padding-top: 20px;
    padding-bottom: 20px;
    .navbar-nav {
        flex-direction: row;
        align-items: center;
    }
    .nav-item {
        display: inline-flex;
        a {
            color: #0791A3;
            padding: 0;
            font-size: 14px;
        }
        &:not(:last-child) {
            margin-right: 3px;
        }
    }
}

.scroll-nav__profile {
    font-size: 14px;
    ul {
        display: flex;
        flex-direction: row;
        list-style: none;
        margin: 0;
        padding: 0;
        li:not(:last-child) {
            margin-right: 20px;
        }
        a {
            color: #0791A3;
            text-decoration: underline;
        }
    }
}

.card {
    background: #FFFFFF 0% 0% no-repeat padding-box;
    box-shadow: 0 3px 10px #7777771A;
    border-radius: 12px;
    opacity: 1;
    border: 0;
}

[class*="sidebar-dark"] .nav-legacy.nav-sidebar>.nav-item>.nav-link.active {
    background: #088493;
}

.minwidth-94 {
    min-width: 94px;
}

@media screen and (min-width: 1200px) {
    .wrap-button__profile {
        padding-left: 20px;
        margin-top: 72px;
    }
    .row {
        margin: 0 -11px;
    }
    .col-1,
    .col-2,
    .col-3,
    .col-4,
    .col-5,
    .col-6,
    .col-7,
    .col-8,
    .col-9,
    .col-10,
    .col-11,
    .col-12,
    .col,
    .col-auto,
    .col-sm-1,
    .col-sm-2,
    .col-sm-3,
    .col-sm-4,
    .col-sm-5,
    .col-sm-6,
    .col-sm-7,
    .col-sm-8,
    .col-sm-9,
    .col-sm-10,
    .col-sm-11,
    .col-sm-12,
    .col-sm,
    .col-sm-auto,
    .col-md-1,
    .col-md-2,
    .col-md-3,
    .col-md-4,
    .col-md-5,
    .col-md-6,
    .col-md-7,
    .col-md-8,
    .col-md-9,
    .col-md-10,
    .col-md-11,
    .col-md-12,
    .col-md,
    .col-md-auto,
    .col-lg-1,
    .col-lg-2,
    .col-lg-3,
    .col-lg-4,
    .col-lg-5,
    .col-lg-6,
    .col-lg-7,
    .col-lg-8,
    .col-lg-9,
    .col-lg-10,
    .col-lg-11,
    .col-lg-12,
    .col-lg,
    .col-lg-auto,
    .col-xl-1,
    .col-xl-2,
    .col-xl-3,
    .col-xl-4,
    .col-xl-5,
    .col-xl-6,
    .col-xl-7,
    .col-xl-8,
    .col-xl-9,
    .col-xl-10,
    .col-xl-11,
    .col-xl-12,
    .col-xl,
    .col-xl-auto {
        padding: 0 11px;
    }
    .main-header {
        height: 80px;
    }
    .layout-fixed .brand-link {
        height: 80px;
        align-items: center;
    }
    .card.card-profile .card-body {
        padding: 50px 42px;
    }
    .theia-content {
        padding-left: 0;
    }
}

@media (max-width: 1770px) {
    .theia-sidebar .btn {
        min-width: 200px;
    }
}

@media (max-width: 1600px) {
    .theia-sidebar .btn {
        min-width: auto;
        width: 45%;
    }
}

@media (max-width: 1240px) {
    .theia-sidebar .btn {
        min-width: auto;
        width: 100%;
        &.full {
            width: 100%;
            min-width: auto;
            margin-right: 0 !important;
        }
    }
    .theia-sidebar .btn[type="reset"] {
        margin-top: 10px;
    }
}

@media(max-width: 1024px) {
    .content-wrapper {
        >.content {
            padding-right: 20px;
            padding-left: 20px;
        }
    }
}

@media(max-width: 767px) {
    .content-wrapper {
        >.content {
            padding-right: 12px;
            padding-left: 12px;
        }
    }
}

@media (max-width: 991px) {
    .main-sidebar,
    .main-sidebar::before {
        box-shadow: none !important;
    }
    .content-wrapper {
        padding-left: 0;
    }
    .theia-sidebar .btn {
        max-width: 200px;
    }
    .theia-sidebar .btn[type="reset"] {
        margin-top: 0px;
    }
}

.custom-range {
    &::-webkit-slider-thumb {
        appearance: none;
        background: $blue;
        cursor: pointer;
        border-radius: 50%
    }
    &::-webkit-slider-thumb:active {
        background-color: $blue;
    }
    &::-webkit-slider-thumb:focus {
        box-shadow: none;
    }
}
.doubleScroll-scroll-wrapper::-webkit-scrollbar{
    height: 10px;
    width: 10px;
}
.doubleScroll-scroll-wrapper::-webkit-scrollbar-thumb {
    background-color: silver;
}
#accordion {
    .card-header {
        border-bottom: 0;
    }
    .card-body {
        border-top: 1px solid rgba(0, 0, 0, 0.125);
        border-top-left-radius: 0;
        border-top-right-radius: 0;
    }
}
#button-redirect-chat{
    position: fixed !important;
    bottom: 5px;
    left: 76px;
    width: 170px;
    height: 41px;
    background-color: #01535D;
    z-index: 9999;
    box-shadow: 0px 3px 6px #00000029;
    border-radius: 25px;
    display: flex;
    justify-content: center;
    align-items: center;
    span{
        margin-left: 9px;
        font-size: 16px;
        line-height: 54px;
        letter-spacing: 0px;
        color: #FFFFFF;
        opacity: 1;
        display: block;
        font-weight: 300 !important;
    }
    img {
        height: 32px;
    }
    @media (max-width: 991px) {
        display: none;
    }
}
#button-redirect-chat-top{
    margin-top: 28px;
    margin-left: 48px;
    width: 130px;
    height: 32px;
    border-radius: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #f5f5f5;
    box-shadow: 0px 3px 6px #00000029;
    span{
        font-size: 20px;
        line-height: 29px;
        letter-spacing: 0px;
        color: #FFFFFF;
        font-weight: 500 !important;
    }
    img {
        margin-left: 9px;
        height: 16px;
        width: 16px;
    }
}
.logo-sidebar-large{
    margin-left: 12px;
    margin-top: 20px;
}
.logo-sidebar-mini{
    margin-top: 20px;
}
.table-responsive{
    table tbody tr td.user-name img{
        flex: 0 0 32px;
    }
}
.badge{
    padding: 5px 10px;
    border-radius: 15px;
    display: initial;
}
