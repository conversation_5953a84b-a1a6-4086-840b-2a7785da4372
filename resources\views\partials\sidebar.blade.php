@php
    use App\Models\Role;
    $currentRouteName = Route::currentRouteName();
    $currentRoutePrefix = Route::current()->getPrefix();
    $request = request();
@endphp

<aside class="main-sidebar sidebar-dark-bee elevation-4">
    <div class="d-flex wrap-brand-link px-2">
        <a @if (Route::has('user.public'))
           href="{{route('user.public', ['id' => auth()->user()->id])}}"
           @endif >
            <img src="{{asset('images/logo.ico')}}" alt="BeeID" class="logo-sidebar-mini d-none">
            <img src="{{asset('images/BeeidLogo.svg ')}}" alt="BeeID" class="logo-sidebar-large">
        </a>
    </div>
    <div class="sidebar">
        <nav class="pb-5 pt-1">
            <ul class="nav nav-pills nav-sidebar flex-column nav-legacy nav-flat" data-widget="treeview" role="menu" data-accordion="false">
                <!-- User management -->
                @if (auth()->user()->hasRole([Role::ROLE_OPERATION_MANAGER, Role::ROLE_SYSTEM_MANAGER]))
                    @php
                        $adminRoutes = [
                            'user.index',
                            'user.create',
                            'user.edit',
                        ];
                    @endphp
                    <li class="nav-item {{ in_array($currentRouteName, $adminRoutes) ? "menu-open" : "" }}">
                        <a href="#" class="nav-link tooltipster 
                            {{ in_array($currentRouteName, $adminRoutes) ? "active" : "" }}"
                            data-tooltip-content="#tooltip_content_user_management">
                            <i class="nav-icon">
                                <img class="icon-nav__item" src="{{ asset('images/nav/employee_management.svg') }}"/>
                            </i>
                            <p>
                                {{trans('language.user_management')}}
                                <i class="right fas fa-angle-left"></i>
                            </p>
                        </a>
                        <div class="tooltip_templates">
                            <span id="tooltip_content_user_management">
                                {{ trans('language.user_management') }}
                            </span>
                        </div>
                        <ul class="nav nav-treeview">
                            <li class="nav-item">
                                <a href="{{route('user.index')}}" class="nav-link tooltipster 
                                    {{ $currentRouteName == 'user.index' || $currentRouteName == 'user.edit' ? "active" : "" }}" 
                                    data-tooltip-content="#tooltip_content_user_list">
                                    <i class="nav-icon fal fa-users-class"></i>
                                    <p>{{trans('language.user_list')}}</p>
                                </a>
                                <div class="tooltip_templates">
                                    <span id="tooltip_content_user_management">
                                        {{ trans('language.user_list') }}
                                    </span>
                                </div>
                            </li>
                            <li class="nav-item">
                                <a href="{{route('user.create')}}" class="nav-link tooltipster 
                                {{ $currentRouteName == 'user.create' ? "active" : "" }}"
                                data-tooltip-content="#tooltip_content_create_user">
                                    <i class="nav-icon fal fa-user-plus"></i>
                                    <p>{{trans('language.create_user')}}</p>
                                </a>
                                <div class="tooltip_templates">
                                    <span id="tooltip_content_create_user">
                                        {{ trans('language.create_user') }}
                                    </span>
                                </div>
                            </li>
                        </ul>
                    </li>
                    <!-- Version management -->
                    @php
                        $versionRoutes = [
                            'version.index',
                            'version.create',
                            'version.edit',
                        ];
                    @endphp
                    <li class="nav-item">
                        <a href="{{ route('versions.index') }}"
                            class="nav-link {{ in_array($currentRouteName, $versionRoutes) ? 'active' : '' }}">
                            <i class="nav-icon fas fa-code-branch"></i>
                            <p>{{ trans('language.version_management') }}</p>
                        </a>
                    </li>
                @endif
            </ul>
        </nav>
    </div>
    <div class="aside-pushmenu">
        <a class="nav-link" data-widget="pushmenu" href="#" role="button">
            <i class="right fas fa-angle-left"></i>
        </a>
    </div>
</aside>