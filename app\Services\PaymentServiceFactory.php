<?php

namespace App\Services;

use App\Contracts\PaymentService;
use App\Enums\PaymentChannel;
use InvalidArgumentException;

class PaymentServiceFactory
{
    public static function make(PaymentChannel|string $channel): PaymentService
    {
        $ch = $channel instanceof PaymentChannel ? $channel : PaymentChannel::from($channel);

        return match ($ch) {
            PaymentChannel::VNPAY => app(VNPayService::class),
            PaymentChannel::MOMO  => app(MomoService::class),
            default => throw new InvalidArgumentException("Unsupported provider: {$channel}")
        };
    }
}
