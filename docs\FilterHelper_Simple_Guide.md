# FilterHelper - Hướng dẫn đơn giản

## Mục tiêu đã đạt được

1. ✅ **Loại bỏ backward compatibility** - không còn hỗ trợ cách cũ
2. ✅ **Bắt buộc truyền type** rõ ràng, mặc định là `text` nếu không truyền
3. ✅ **Switch case** dễ hiểu và có thể bổ sung thêm case
4. ✅ **Enum quản lý** các type có sẵn
5. ✅ **Code ngắn gọn** và dễ đọc

## Cách sử dụng

### Cấu trúc config bắt buộc:
```php
$config = [
    ['field' => 'tên_field', 'type' => 'loại_type', 'options' => [...]],
    // ...
];
```

### Ví dụ thực tế:

#### UserController:
```php
$filterConfig = [
    ['field' => 'id', 'type' => 'ids', 'options' => ['prefix' => '#']],
    ['field' => 'keyword', 'type' => 'text', 'options' => ['max_length' => 20]], // max_length là tùy chọn
    ['field' => 'email', 'type' => 'text'], // type mặc định là text nếu không truyền
    ['field' => 'phone', 'type' => 'text'],
    ['field' => 'gender', 'type' => 'array', 'options' => [
        'mapping' => trans('language.genders')
    ]]
];
```

#### VersionController:
```php
$filterConfig = [
    ['field' => 'search', 'type' => 'text', 'options' => ['prefix' => 'Search: ']],
    ['field' => 'platform', 'type' => 'select', 'options' => [
        'mapping' => [
            AppVersion::IOS => __('language.ios'),
            AppVersion::ANDROID => __('language.android')
        ]
    ]],
    ['field' => 'status', 'type' => 'select', 'options' => [
        'mapping' => [
            AppVersion::PUBLISHED => __('language.published'),
            AppVersion::DRAFT => __('language.draft')
        ]
    ]]
];
```

## Các type được hỗ trợ (7 types cơ bản)

### 1. `text` (mặc định)
- Hiển thị text đơn giản
- Options: `max_length`, `prefix`, `suffix`
- **Không có max mặc định** - chỉ khi truyền vào options

### 2. `ids`
- Danh sách ID phân tách bằng dấu phẩy
- Options: `prefix` (mặc định: '#'), `separator` (mặc định: ',')

### 3. `array`
- Mảng giá trị với mapping
- Options: `mapping` (array ánh xạ giá trị -> nhãn)

### 4. `select`
- Giá trị select/enum với mapping
- Options: `mapping` (array ánh xạ giá trị -> nhãn)

### 5. `boolean`
- Giá trị true/false
- Options: `labels` (array [false_label, true_label])

### 6. `date`
- Ngày tháng với format
- Options: `format` (mặc định: 'd/m/Y')

### 7. `range`
- Khoảng giá trị (from-to)
- Options: `separator`, `prefix`, `suffix`

## Switch case trong FilterHelper

```php
private function processFieldByType($value, string $type, string $tagOpen, string $tagClose, array $options = []): string
{
    switch ($type) {
        case 'ids':
            return $this->formatIds($value, $tagOpen, $tagClose, $options);
        case 'array':
            return $this->formatArray($value, $tagOpen, $tagClose, $options);
        case 'select':
            return $this->formatSelect($value, $tagOpen, $tagClose, $options);
        case 'boolean':
            return $this->formatBoolean($value, $tagOpen, $tagClose, $options);
        case 'date':
            return $this->formatDate($value, $tagOpen, $tagClose, $options);
        case 'range':
            return $this->formatRange($value, $tagOpen, $tagClose, $options);
        case 'text':
        default:
            return $this->formatText($value, $tagOpen, $tagClose, $options);
    }
}
```

**Giải thích:** Switch case này rất dễ bổ sung thêm case mới. Chỉ cần:
1. Thêm `case 'new_type':` 
2. Tạo method `formatNewType()`
3. Thêm type vào enum (tùy chọn)

## Enum quản lý types

```php
enum FilterFieldType: string
{
    case TEXT = 'text';
    case IDS = 'ids';
    case ARRAY = 'array';
    case SELECT = 'select';
    case BOOLEAN = 'boolean';
    case DATE = 'date';
    case RANGE = 'range';
}
```

**Lợi ích:**
- Quản lý tập trung các type có sẵn
- Có thể dùng `FilterFieldType::getAllTypes()` để lấy danh sách
- Có thể dùng `FilterFieldType::isValid($type)` để validate
- Có thể dùng `FilterFieldType::createConfig()` để tạo config nhanh

## Sử dụng với Enum (tùy chọn)

```php
use App\Enums\FilterFieldType;

$config = [
    FilterFieldType::createConfig('keyword', FilterFieldType::TEXT, ['max_length' => 20]),
    FilterFieldType::createConfig('status', FilterFieldType::SELECT, [
        'mapping' => ['active' => 'Active', 'inactive' => 'Inactive']
    ])
];
```

## Kết luận

FilterHelper bây giờ:
- **Đơn giản** và dễ hiểu
- **Bắt buộc type** rõ ràng
- **Dễ mở rộng** với switch case
- **Có enum** để quản lý nhưng không bắt buộc
- **Code ngắn gọn** và maintainable
