<?php

namespace App\Models;

use App\Enums\OrderStatus;
use App\Enums\OrderType;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    use HasUuids;

    protected $fillable = [
        'user_id','type','code','amount','currency','status','meta','paid_at'
    ];

    protected $casts = [
        'type'    => OrderType::class,
        'status'  => OrderStatus::class,
        'meta'    => 'array',
        'paid_at' => 'datetime',
    ];

    public function payments() {
        return $this->hasMany(Payment::class);
    }
}
