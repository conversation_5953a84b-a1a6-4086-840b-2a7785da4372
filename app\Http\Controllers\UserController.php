<?php

namespace App\Http\Controllers;

use App\Helpers\FilterHelper;
use App\Http\Requests\UserRequest;
use App\Logics\UserManager;
use App\Models\Province;
use App\Models\Role;
use App\Models\User;
use App\Models\Ward;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class UserController extends Controller
{
    private $userManager;
    private $filterHelper;

    public function __construct(
        UserManager $userManager,
        FilterHelper $filterHelper
    ) {
        $this->userManager = $userManager;
        $this->filterHelper = $filterHelper;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Get all users
        $users = $this->userManager->getUserList($request);

        // Pagination
        $perPage = $request->has('per_page') ? $request->input('per_page') : PER_PAGE;
        $users = $users->paginate($perPage);

        if ($redirect = $this->redirectInvalidPage($users, $request)) {
            return $redirect;
        }

        $fields = ['id', 'keyword', 'email', 'phone', 'gender'];
        $isFilter = $this->filterHelper->renderFilterBadges($request, $fields);

        return view('user.index', [
            'users' => $users,
            'is_filter' => $isFilter,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $isAdmin = auth()->user()->hasRole(Role::ROLE_SYSTEM_MANAGER);
        $canEdit = auth()->user()->hasAnyRole([Role::ROLE_SYSTEM_MANAGER, Role::ROLE_OPERATION_MANAGER]);
        $allowedRoles = Role::select('name')
            ->when(!$isAdmin, function ($q) {
                $q->where('name', '!=', Role::ROLE_SYSTEM_MANAGER);
            })
            ->orderBy('id', 'asc')
            ->get();
        $provinces = Province::select('province_code', 'name')
            ->orderBy('id')->get();
        $wards = [];

        return view('user.edit', [
            'allowedRoles' => $allowedRoles,
            'canEdit' => $canEdit,
            'prefectures' => $provinces,
            'communes' => $wards,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(UserRequest $request)
    {
        try {
            $this->userManager->createUser($request);

            return redirect()->route('user.index')->with([
                'status_succeed' => trans('message.success')
            ]);
        } catch (Exception $e) {
            Log::error($e->getMessage() . "\n" . $e->getTraceAsString());
            return redirect()->route('user.index')->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $user = User::find($id);

        // Return error message if user not exist
        if ($user === null) {
            return back()->with([
                'status_failed' => trans('message.user_not_exist')
            ]);
        }
        $userRoles = $user->roles->pluck('name')->toArray();
        $isAdmin = auth()->user()->hasRole(Role::ROLE_SYSTEM_MANAGER);
        $canEdit = $user->deleted_at == null 
            && auth()->user()->hasAnyRole([Role::ROLE_SYSTEM_MANAGER, Role::ROLE_OPERATION_MANAGER]);
        $allowedRoles = Role::select('name')
            ->when(!$isAdmin, function ($q) {
                $q->where('name', '!=', Role::ROLE_SYSTEM_MANAGER);
            })
            ->orderBy('id', 'asc')
            ->get();
        $provinces = Province::select('province_code', 'name')
            ->orderBy('id')->get();
        $wards = $user->province_code == null ? [] :
            Ward::select('ward_code', 'name')
                ->where('province_code', $user->province_code)
                ->orderBy('id')->get();

        return view('user.edit', [
            'user' => $user,
            'userRoles' => $userRoles,
            'allowedRoles' => $allowedRoles,
            'canEdit' => $canEdit,
            'prefectures' => $provinces,
            'communes' => $wards,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UserRequest $request, string $id)
    {
        try {
            $result = $this->userManager->updateUser($request, $id);

            if (!$result) {
                return back()->with([
                    'status_failed' => trans('message.user_not_exist')
                ]);
            }

            return back()->with([
                'status_succeed' => trans('message.success')
            ]);
        } catch (Exception $e) {
            Log::error($e->getMessage() . "\n" . $e->getTraceAsString());
            return back()->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $result = $this->userManager->deleteUser($id);

        if (!$result) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => trans('language.failure'),
                    'text' => trans('message.user_not_exist'),
                ],
            ];
        }

        return [
            'status' => Response::HTTP_OK,
            'msg' => [
                'title' => trans('language.success'),
                'text' => trans('message.delete_user_succeed'),
            ],
        ];
    }

    /**
     * Get the avatar image for a user.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function getAvatar($id)
    {
        $image = $this->userManager->getAvatar($id, 'avatar');
        return $image;
    }
}
