<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Helpers\FilterHelper;
use App\Enums\FilterType;
use Illuminate\Http\Request;

class FilterHelperWithEnumTest extends TestCase
{
    private $filterHelper;

    protected function setUp(): void
    {
        parent::setUp();
        $this->filterHelper = new FilterHelper();
    }

    public function test_it_works_with_enum_config()
    {
        $request = new Request(['keyword' => 'test']);

        $config = [
            ['field' => 'keyword', 'type' => FilterType::TEXT->value]
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);

        $this->assertStringContainsString('test', $result);
        $this->assertStringContainsString('badge', $result);
    }

    public function test_it_works_with_enum_ids_type()
    {
        $request = new Request(['user_ids' => '1,2,3']);
        
        $config = [
            FilterType::createConfig('user_ids', FilterType::IDS, ['prefix' => 'User #'])
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);
        
        $this->assertStringContainsString('User #1', $result);
        $this->assertStringContainsString('User #2', $result);
        $this->assertStringContainsString('User #3', $result);
    }

    public function test_it_works_with_enum_select_type()
    {
        $request = new Request(['status' => 'active']);
        
        $config = [
            FilterType::createConfig('status', FilterType::SELECT, [
                'mapping' => ['active' => 'Đang hoạt động', 'inactive' => 'Không hoạt động']
            ])
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);
        
        $this->assertStringContainsString('Đang hoạt động', $result);
    }

    public function test_custom_formatter_still_works()
    {
        $request = new Request(['user_id' => '123']);
        
        $config = [
            [
                'field' => 'user_id',
                'type' => FilterType::TEXT->value, // Có thể dùng enum value
                'formatter' => function($value, $tagOpen, $tagClose, $options) {
                    // Custom logic: format user ID thành user name
                    $userName = "User " . $value; // Giả lập query user
                    return $tagOpen . $userName . $tagClose;
                }
            ]
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);
        
        $this->assertStringContainsString('User 123', $result);
    }

    public function test_custom_formatter_with_database_simulation()
    {
        $request = new Request(['category_id' => '5']);
        
        $config = [
            [
                'field' => 'category_id',
                'formatter' => function($value, $tagOpen, $tagClose, $options) {
                    // Giả lập query database
                    $categories = [
                        '1' => 'Technology',
                        '2' => 'Business', 
                        '5' => 'Education'
                    ];
                    
                    $categoryName = $categories[$value] ?? 'Unknown';
                    
                    // Custom HTML với class khác
                    $customTag = str_replace('badge-primary', 'badge-success', $tagOpen);
                    
                    return $customTag . $categoryName . $tagClose;
                }
            ]
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);
        
        $this->assertStringContainsString('Education', $result);
        $this->assertStringContainsString('badge-success', $result);
    }

    public function test_custom_formatter_with_complex_logic()
    {
        $request = new Request(['amount' => '1500000']);
        
        $config = [
            [
                'field' => 'amount',
                'formatter' => function($value, $tagOpen, $tagClose, $options) {
                    $amount = (int) $value;
                    
                    // Custom logic phức tạp
                    if ($amount > 1000000) {
                        $displayValue = number_format($amount/1000000, 1) . 'M VNĐ';
                        $customTag = str_replace('badge-primary', 'badge-danger', $tagOpen);
                    } elseif ($amount > 1000) {
                        $displayValue = number_format($amount/1000, 1) . 'K VNĐ';
                        $customTag = str_replace('badge-primary', 'badge-warning', $tagOpen);
                    } else {
                        $displayValue = number_format($amount) . ' VNĐ';
                        $customTag = $tagOpen;
                    }
                    
                    return $customTag . $displayValue . $tagClose;
                }
            ]
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);
        
        $this->assertStringContainsString('1.5M VNĐ', $result);
        $this->assertStringContainsString('badge-danger', $result);
    }

    public function test_enum_helper_methods()
    {
        // Test getAllTypes
        $types = FilterType::getAllTypes();
        $this->assertContains('text', $types);
        $this->assertContains('ids', $types);
        $this->assertContains('array', $types);
        $this->assertContains('select', $types);
        $this->assertContains('boolean', $types);
        $this->assertContains('date', $types);
        $this->assertContains('range', $types);
        
        // Test isValid
        $this->assertTrue(FilterType::isValid('text'));
        $this->assertTrue(FilterType::isValid('ids'));
        $this->assertFalse(FilterType::isValid('invalid'));
        
        // Test createConfig
        $config = FilterType::createConfig('test', FilterType::TEXT, ['max_length' => 20]);
        $this->assertEquals('test', $config['field']);
        $this->assertEquals('text', $config['type']);
        $this->assertEquals(['max_length' => 20], $config['options']);
        
        // Test getDescription
        $description = FilterType::TEXT->getDescription();
        $this->assertStringContainsString('Text đơn giản', $description);
        
        // Test getDefaultOptions
        $options = FilterType::IDS->getDefaultOptions();
        $this->assertEquals('#', $options['prefix']);
        $this->assertEquals(',', $options['separator']);
    }

    public function test_backward_compatibility_still_works()
    {
        $request = new Request(['test' => 'value']);
        
        // Vẫn có thể dùng cách cũ với string
        $config = [
            ['field' => 'test', 'type' => 'text'] // String thay vì enum
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);
        
        $this->assertStringContainsString('value', $result);
    }

    public function test_mixed_enum_and_string_types()
    {
        $request = new Request(['field1' => 'value1', 'field2' => 'value2']);
        
        $config = [
            FilterType::createConfig('field1', FilterType::TEXT), // Dùng enum
            ['field' => 'field2', 'type' => 'text'] // Dùng string
        ];

        $result = $this->filterHelper->renderFilterBadges($request, $config);
        
        $this->assertStringContainsString('value1', $result);
        $this->assertStringContainsString('value2', $result);
    }
}
