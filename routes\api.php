<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\PaymentController;
use App\Http\Controllers\Api\CheckVersionController;
use Illuminate\Support\Facades\Route;

Route::group(['namespace' => 'Api', 'as'=>'api.'], function () {
    Route::group(['prefix' => 'auth', 'as'=>'auth.'], function () {
        Route::post('login', [AuthController::class, 'login']);
        Route::post('logout', [AuthController::class, 'logout'])->middleware('auth:api');
        Route::post('refresh', [AuthController::class, 'refresh'])->middleware('auth:api');
    });

    // Check version
    Route::post('check-version', [CheckVersionController::class, 'checkVersion']);

    Route::group(['middleware' => 'auth:api'], function () {
        // Payment
        Route::post('/payments/start', [PaymentController::class, 'start']);
        // Version
        Route::get('test-middleware', function () {
            return response()->json([
                'message' => 'Test middleware success'
            ]);
        });
    });

    // Payment
    Route::get('/payments/return/{channel}', [PaymentController::class, 'return']);
    Route::match(['GET','POST'], '/payments/ipn/{channel}', [PaymentController::class, 'ipn']);
});
